<template>
  <div class="min-h-screen bg-gradient-to-br from-orange-50 to-amber-100 dark:from-orange-900/20 dark:to-amber-900/20">
    <div class="container mx-auto px-4 py-8">
      <!-- Header -->
      <div class="text-center mb-8">
        <UButton
          @click="$router.push('/')"
          variant="ghost"
          icon="i-lucide-arrow-left"
          size="lg"
          class="mb-6"
        >
          Back to Home
        </UButton>

        <h1 class="text-4xl font-bold text-orange-900 dark:text-orange-100 mb-4">
          Piecewise Functions Assessment
        </h1>
        <p class="text-xl text-orange-700 dark:text-orange-300 max-w-2xl mx-auto">
          Teacher-led assessment tool for piecewise function evaluation practice
        </p>
      </div>

      <!-- Display Mode -->
      <PiecewiseDisplayMode />
    </div>
  </div>
</template>

<script setup lang="ts">
// SEO Meta
useSeoMeta({
  title: 'Piecewise Functions Assessment - Math Assessment Center',
  description: 'Teacher-led assessment tool for piecewise function evaluation practice with step-by-step solutions and mathematical accuracy.',
})

// Store
const gameStore = useGameStore()

// Initialize game on mount
onMounted(() => {
  gameStore.initializeGame('piecewise-functions')
})
</script>
