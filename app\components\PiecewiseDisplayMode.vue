<template>
  <div class="max-w-none lg:max-w-[1600px] mx-auto px-4">
    <!-- Two-Column Layout with increased width for mathematical expressions -->
    <div class="grid lg:grid-cols-2 gap-8">
      <!-- Left Column: Problem Display -->
      <div class="space-y-6">
        <UCard class="overflow-x-auto">
          <template #header>
            <div class="flex items-center justify-between">
              <h2 class="text-2xl font-semibold text-gray-900 dark:text-white">
                Piecewise Functions Practice
              </h2>
              <UBadge color="primary" variant="soft" size="lg">
                Display Mode
              </UBadge>
            </div>
          </template>

          <div class="space-y-8">
            <!-- Task Description -->
            <div class="text-center">
              <div class="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">
                Task
              </div>
              <UBadge
                color="warning"
                variant="soft"
                size="lg"
                class="text-lg px-4 py-2 font-extrabold"
              >
                {{ currentProblem?.description }}
              </UBadge>
            </div>

            <!-- Piecewise Function Display -->
            <div class="text-center">
              <div class="text-lg font-medium text-gray-600 dark:text-gray-400 mb-4">
                Piecewise Function
              </div>
              <div class="p-8 bg-orange-50 dark:bg-orange-900/20 rounded-xl border-2 border-orange-200 dark:border-orange-700 min-w-0 overflow-x-auto">
                <div class="text-orange-900 dark:text-orange-100">
                  <!-- Function definition with proper piecewise notation -->
                  <div class="piecewise-function">
                    <span class="function-equation">{{ currentProblem?.functionLetter }}(x) = </span>
                    <div class="piecewise-cases">
                      <div class="piecewise-brace">{</div>
                      <div class="piecewise-pieces">
                        <div
                          v-for="(piece, index) in currentProblem?.pieces"
                          :key="index"
                          class="piecewise-piece"
                        >
                          <span class="piecewise-piece-equation" v-html="formatMathematicalExpression(piece.equation)"></span>
                          <span class="piecewise-condition">if {{ piece.condition }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <template #footer>
            <div class="flex justify-center gap-4">
              <UButton
                @click="generateNewProblem"
                :loading="generating"
                icon="i-lucide-refresh-cw"
                size="lg"
                color="primary"
              >
                Generate New Problem
              </UButton>
            </div>
          </template>
        </UCard>
      </div>

      <!-- Right Column: Answer Area -->
      <div class="space-y-6">
        <UCard class="min-h-[400px] overflow-x-auto">
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                Solution
              </h3>
              <UButton
                @click="toggleAnswer"
                :icon="showAnswer ? 'i-lucide-eye-off' : 'i-lucide-eye'"
                variant="outline"
                size="sm"
              >
                {{ showAnswer ? 'Hide Answer' : 'Show Answer' }}
              </UButton>
            </div>
          </template>

          <div v-if="!showAnswer" class="flex items-center justify-center h-64">
            <div class="text-center">
              <Icon name="i-lucide-eye-off" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-500 dark:text-gray-400 text-lg">
                Click "Show Answer" to reveal the solution
              </p>
            </div>
          </div>

          <div v-if="showAnswer && currentProblem" class="space-y-6">
            <!-- Step 1: Identify applicable piece -->
            <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg overflow-x-auto">
              <div class="flex items-center space-x-2 mb-3">
                <UBadge color="primary" variant="soft" size="sm">Step 1</UBadge>
                <span class="font-medium">Identify the applicable piece</span>
              </div>
              <p class="math-answer text-blue-800 dark:text-blue-200">
                {{ currentProblem.answer.explanation }}
              </p>
            </div>

            <!-- Step 2: Substitution -->
            <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg overflow-x-auto">
              <div class="flex items-center space-x-2 mb-3">
                <UBadge color="success" variant="soft" size="sm">Step 2</UBadge>
                <span class="font-medium">Substitute the value</span>
              </div>
              <p class="math-answer text-green-800 dark:text-green-200" v-html="formatMathematicalExpression(currentProblem.answer.substitutionStep)">
              </p>
            </div>

            <!-- Step 3: Final Result -->
            <div class="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg overflow-x-auto">
              <div class="flex items-center space-x-2 mb-3">
                <UBadge color="secondary" variant="soft" size="sm">Final Answer</UBadge>
              </div>
              <p class="math-expression-large text-purple-800 dark:text-purple-200" v-html="formatMathematicalExpression(`${currentProblem.functionLetter}(${currentProblem.evaluationPoint}) = ${currentProblem.answer.finalResult}`)">
              </p>
            </div>
          </div>
        </UCard>

        <!-- Problem History -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              Recent Problems
            </h3>
          </template>
          
          <div v-if="problemHistory.length === 0" class="text-center py-8">
            <Icon name="i-lucide-history" class="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <p class="text-gray-500 dark:text-gray-400">
              No previous problems yet. Generate some problems to see history!
            </p>
          </div>
          
          <div class="space-y-3">
            <div 
              v-for="(problem, index) in problemHistory.slice(-5).reverse()" 
              :key="problem.id"
              class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
            >
              <div class="flex items-center space-x-3">
                <UBadge 
                  color="warning" 
                  variant="soft"
                  size="sm"
                >
                  {{ problem.description }}
                </UBadge>
                <span class="math-expression-small text-gray-700 dark:text-gray-300">
                  {{ problem.functionLetter }}(x) with {{ problem.pieces.length }} pieces
                </span>
              </div>
              <span class="text-xs text-gray-500 dark:text-gray-400">
                {{ index === 0 ? 'Current' : `${index + 1} ago` }}
              </span>
            </div>
          </div>
        </UCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { DisplayPiecewiseProblem } from '~/types/game'
import { generateDisplayPiecewiseProblem } from '~/utils/problemGenerator'

// State
const currentProblem = ref<DisplayPiecewiseProblem | null>(null)
const generating = ref(false)
const problemHistory = ref<DisplayPiecewiseProblem[]>([])
const showAnswer = ref(false)

// Methods
const generateNewProblem = async () => {
  generating.value = true
  
  try {
    // Small delay for better UX
    await new Promise(resolve => setTimeout(resolve, 200))
    
    const newProblem = generateDisplayPiecewiseProblem()
    
    // Add current problem to history before replacing
    if (currentProblem.value) {
      problemHistory.value.push(currentProblem.value)
    }
    
    currentProblem.value = newProblem
    // Hide answer when generating new problem
    showAnswer.value = false
  } finally {
    generating.value = false
  }
}

const toggleAnswer = () => {
  showAnswer.value = !showAnswer.value
}

/**
 * Format mathematical expressions with proper square root rendering
 */
const formatMathematicalExpression = (expression: string): string => {
  if (!expression) return ''

  // Enhanced square root formatting with proper radical symbol and overline
  return expression
    // Handle square root with parentheses - improved spacing and structure
    .replace(/√\(([^)]+)\)/g, '<span class="math-sqrt-classroom"><span class="sqrt-symbol">√</span><span class="sqrt-radicand-classroom">$1</span></span>')
    // Handle simple square root expressions
    .replace(/√([a-zA-Z0-9]+)/g, '<span class="math-sqrt-classroom"><span class="sqrt-symbol">√</span><span class="sqrt-radicand-classroom">$1</span></span>')
    // Format superscripts for exponents
    .replace(/x²/g, 'x<sup>2</sup>')
    .replace(/x³/g, 'x<sup>3</sup>')
    // Format absolute value bars
    .replace(/\|([^|]+)\|/g, '|$1|')
    // Ensure proper spacing around operators
    .replace(/([+-])/g, ' $1 ')
    .replace(/\s+/g, ' ')
    .trim()
}

// Initialize with first problem
onMounted(() => {
  generateNewProblem()
})
</script>

<style scoped>
/* Piecewise function layout styling - typography handled by global classes */
.piecewise-function {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap; /* Allow wrapping for smaller screens */
}

.piecewise-cases {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.piecewise-pieces {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.piecewise-piece {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  min-height: 2.5rem;
  flex-wrap: wrap; /* Allow wrapping for long expressions */
}

/* Enhanced square root styling for classroom display - optimized for projector visibility */
.math-sqrt-classroom {
  position: relative;
  display: inline-block;
  font-size: inherit;
  font-family: 'Latin Modern Math', 'Cambria Math', 'STIX Two Math', serif;
  margin-right: 0.1em; /* Small spacing after the entire square root expression */
  line-height: 1;
}

/* Style the √ symbol specifically */
.math-sqrt-classroom .sqrt-symbol {
  display: inline-block;
  font-size: 1.2em; /* Slightly larger √ symbol */
  vertical-align: -0.15em; /* Position √ symbol to align with baseline */
  margin-right: 0.05em; /* Small gap between √ and radicand */
  font-weight: 500;
}

.math-sqrt-classroom .sqrt-radicand-classroom {
  border-top: 3px solid currentColor; /* Thicker overline for classroom visibility */
  padding-top: 0.05em; /* Minimal padding to prevent overline from touching content */
  padding-left: 0.08em; /* Small left padding for better appearance */
  padding-right: 0.08em; /* Small right padding to extend overline slightly */
  margin-left: 0.05em; /* Minimal spacing between √ symbol and radicand */
  display: inline-block;
  min-width: 1.2em; /* Ensure minimum width for proper overline display */
  vertical-align: baseline; /* Keep radicand aligned with the √ symbol */
}

/* Superscript styling */
sup {
  font-size: 0.75em;
  vertical-align: super;
  line-height: 0;
}

/* Responsive adjustments for mathematical expressions */
@media (max-width: 768px) {
  .piecewise-function {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .piecewise-piece {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
</style>
