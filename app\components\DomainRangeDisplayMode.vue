<template>
  <div class="max-w-none lg:max-w-[1600px] mx-auto px-4">
    <!-- Two-Column Layout with increased width for mathematical expressions -->
    <div class="grid lg:grid-cols-2 gap-8">
      <!-- Left Column: Problem Display -->
      <div class="space-y-6">
        <UCard class="overflow-x-auto">
          <template #header>
            <div class="flex items-center justify-between">
              <h2 class="text-2xl font-semibold text-gray-900 dark:text-white">
                Domain & Range Practice
              </h2>
              <UBadge color="primary" variant="soft" size="lg">
                Display Mode
              </UBadge>
            </div>
          </template>

          <div class="space-y-8">
            <!-- Question Type -->
            <div class="text-center">
              <div class="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">
                Task
              </div>
              <UBadge
                :color="getQuestionColor(currentProblem?.questionType)"
                variant="soft"
                size="lg"
                class="text-lg px-4 py-2 font-extrabold"
              >
                {{ currentProblem?.description }}
              </UBadge>
            </div>

            <!-- Function Display -->
            <div class="text-center">
              <div class="text-lg font-medium text-gray-600 dark:text-gray-400 mb-4">
                Function
              </div>
              <div class="p-8 bg-green-50 dark:bg-green-900/20 rounded-xl border-2 border-green-200 dark:border-green-700 min-w-0 overflow-x-auto">
                <div class="text-green-900 dark:text-green-100 function-equation leading-relaxed">
                  {{ currentProblem?.equation }}
                </div>
              </div>
            </div>

            <!-- Instructions -->
            <div class="text-center">
              <p class="text-gray-600 dark:text-gray-400 text-lg">
                This function is displayed for teacher-led assessment and discussion.
              </p>
            </div>
          </div>

          <template #footer>
            <div class="flex justify-center gap-4">
              <UButton
                @click="toggleAnswer"
                size="lg"
                :icon="showAnswer ? 'i-lucide-eye-off' : 'i-lucide-eye'"
                variant="outline"
              >
                {{ showAnswer ? 'Hide Answer' : 'View Answer' }}
              </UButton>

              <UButton
                @click="generateNewProblem"
                size="lg"
                icon="i-lucide-refresh-cw"
                :loading="generating"
              >
                Generate New Problem
              </UButton>
            </div>
          </template>
        </UCard>
      </div>

      <!-- Right Column: Answer Display -->
      <div class="space-y-6">
        <UCard
          v-if="currentProblem"
          :class="[
            'min-h-[400px] transition-all duration-300',
            showAnswer
              ? 'border-2 border-green-200 dark:border-green-800 bg-white dark:bg-gray-900'
              : 'border-2 border-gray-200 dark:border-gray-700 bg-gray-50/30 dark:bg-gray-800/10'
          ]"
        >
          <template #header>
            <div class="flex items-center space-x-3">
              <div
                :class="[
                  'w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-300',
                  showAnswer
                    ? 'bg-green-100 dark:bg-green-900'
                    : 'bg-gray-100 dark:bg-gray-800'
                ]"
              >
                <Icon
                  :name="showAnswer ? 'i-lucide-check-circle' : 'i-lucide-eye-off'"
                  :class="[
                    'w-5 h-5 transition-colors duration-300',
                    showAnswer
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-gray-400 dark:text-gray-500'
                  ]"
                />
              </div>
              <h3
                :class="[
                  'text-xl font-semibold transition-colors duration-300',
                  showAnswer
                    ? 'text-green-900 dark:text-green-100'
                    : 'text-gray-500 dark:text-gray-400'
                ]"
              >
                {{ showAnswer ? 'Correct Answers' : 'Answer Area' }}
              </h3>
            </div>
          </template>

          <div v-if="showAnswer" class="space-y-4">
            <div class="grid gap-4">
              <!-- Domain -->
              <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700 overflow-x-auto">
                <div class="flex items-center space-x-2 mb-3">
                  <UBadge color="primary" variant="soft" size="sm">Domain</UBadge>
                </div>
                <div class="space-y-3">
                  <div>
                    <span class="text-sm font-semibold text-blue-800 dark:text-blue-200 block mb-1">Interval Notation:</span>
                    <p class="interval-notation math-answer-domain">
                      {{ currentProblem.answer.domain }}
                    </p>
                  </div>
                  <div>
                    <span class="text-sm font-semibold text-blue-800 dark:text-blue-200 block mb-1">Set-Builder Notation:</span>
                    <p class="set-builder-notation math-answer-domain">
                      {{ currentProblem.answer.domainSetBuilder }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- Range -->
              <div class="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-700 overflow-x-auto">
                <div class="flex items-center space-x-2 mb-3">
                  <UBadge color="secondary" variant="soft" size="sm">Range</UBadge>
                </div>
                <div class="space-y-3">
                  <div>
                    <span class="text-sm font-semibold text-purple-800 dark:text-purple-200 block mb-1">Interval Notation:</span>
                    <p class="interval-notation math-answer-range">
                      {{ currentProblem.answer.range }}
                    </p>
                  </div>
                  <div>
                    <span class="text-sm font-semibold text-purple-800 dark:text-purple-200 block mb-1">Set-Builder Notation:</span>
                    <p class="set-builder-notation math-answer-range">
                      {{ currentProblem.answer.rangeSetBuilder }}
                    </p>
                  </div>
                </div>
              </div>
            </div>


          </div>

          <div v-else class="flex items-center justify-center h-64">
            <div class="text-center space-y-4">
              <Icon name="i-lucide-eye-off" class="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto" />
              <p class="text-gray-500 dark:text-gray-400 text-lg">
                Click "View Answer" to display the solutions
              </p>
              <p class="text-gray-400 dark:text-gray-500 text-sm">
                Domain and range answers will appear here for classroom discussion
              </p>
            </div>
          </div>
        </UCard>
      </div>
    </div>

    <!-- Problem History (Optional) -->
    <UCard v-if="problemHistory.length > 1">
      <template #header>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          Recent Problems
        </h3>
      </template>
      
      <div class="space-y-3">
        <div 
          v-for="(problem, index) in problemHistory.slice(-5).reverse()" 
          :key="problem.id"
          class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <UBadge 
              :color="getQuestionColor(problem.questionType)" 
              variant="soft"
              size="sm"
            >
              {{ problem.description }}
            </UBadge>
            <span class="function-equation text-sm text-gray-700 dark:text-gray-300">
              {{ problem.equation }}
            </span>
          </div>
          <span class="text-xs text-gray-500 dark:text-gray-400">
            {{ index === 0 ? 'Current' : `${index + 1} ago` }}
          </span>
        </div>
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
import type { DisplayDomainRangeProblem, FunctionType } from '~/types/game'
import { generateDisplayDomainRangeProblem } from '~/utils/problemGenerator'



// State
const currentProblem = ref<DisplayDomainRangeProblem | null>(null)
const generating = ref(false)
const problemHistory = ref<DisplayDomainRangeProblem[]>([])
const showAnswer = ref(false)

// Methods
const generateNewProblem = async () => {
  generating.value = true
  
  try {
    // Small delay for better UX
    await new Promise(resolve => setTimeout(resolve, 200))
    
    const newProblem = generateDisplayDomainRangeProblem()
    
    // Add current problem to history before replacing
    if (currentProblem.value) {
      problemHistory.value.push(currentProblem.value)
    }
    
    currentProblem.value = newProblem
    // Hide answer when generating new problem
    showAnswer.value = false
  } finally {
    generating.value = false
  }
}

const toggleAnswer = () => {
  showAnswer.value = !showAnswer.value
}

const getQuestionColor = (questionType?: string) => {
  switch (questionType) {
    case 'domain':
      return 'primary'
    case 'range':
      return 'secondary'
    default:
      return 'neutral'
  }
}

const getFunctionTypeColor = (functionType: FunctionType) => {
  switch (functionType) {
    case 'linear':
      return 'success'
    case 'quadratic':
      return 'primary'
    case 'cubic':
      return 'secondary'
    case 'absolute':
      return 'warning'
    case 'square-root':
      return 'error'
    default:
      return 'neutral'
  }
}

const getFunctionTypeName = (functionType: FunctionType) => {
  switch (functionType) {
    case 'linear':
      return 'Linear Function'
    case 'quadratic':
      return 'Quadratic Function'
    case 'cubic':
      return 'Cubic Function'
    case 'absolute':
      return 'Absolute Value Function'
    case 'square-root':
      return 'Square Root Function'
    default:
      return 'Function'
  }
}

// Initialize with first problem
onMounted(() => {
  generateNewProblem()
})
</script>
