import type {
  DisplayDomainRangeProblem,
  DisplayNotationProblem,
  DisplayPiecewiseProblem,
  FunctionLetter,
  FunctionType,
  NotationAnswer,
  PiecewiseAnswer,
  PiecewisePiece,
} from '~/types/game'

/**
 * Generate random integer within range
 */
function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * Generate random coefficient (avoiding 0)
 */
function randomCoeff(): number {
  const coeff = randomInt(-5, 5)
  return coeff === 0 ? 1 : coeff
}

/**
 * Generate display-only notation problems for teacher assessment
 */
export function generateDisplayNotationProblem(): DisplayNotationProblem {
  const id = `display-notation-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

  const types: Array<'english' | 'set-builder' | 'interval'> = [
    'english',
    'set-builder',
    'interval',
  ]
  const type = types[randomInt(0, types.length - 1)]!

  // Define statement structures
  const statementTypes = [
    'all-real',
    'greater-than',
    'greater-equal',
    'less-than',
    'less-equal',
    'between-open',
    'between-closed',
    'between-left-closed',
    'between-right-closed',
  ]

  const statementType = statementTypes[randomInt(0, statementTypes.length - 1)]!

  let content = ''
  let description = ''

  // Generate random values once for consistency across all notation formats
  const a = randomInt(-10, 10)
  const b = randomInt(a + 1, a + 10) // Ensure b > a for intervals

  // Generate all three formats for the same mathematical concept
  const answer: NotationAnswer = {
    english: generateEnglishStatement(statementType, a, b),
    setBuilder: generateSetBuilderNotation(statementType, a, b, 'x'),
    interval: generateIntervalNotation(statementType, a, b),
  }

  switch (type) {
    case 'english':
      description = 'English Statement'
      content = answer.english
      break

    case 'set-builder':
      description = 'Set-Builder Notation'
      content = answer.setBuilder
      break

    case 'interval':
      description = 'Interval Notation'
      content = answer.interval
      break
  }

  return {
    id,
    type,
    content,
    description,
    answer,
  }
}

function generateEnglishStatement(statementType: string, a: number, b: number): string {
  switch (statementType) {
    case 'all-real':
      return 'all real numbers'
    case 'greater-than':
      return `all numbers greater than ${a}`
    case 'greater-equal':
      return `all numbers greater than or equal to ${a}`
    case 'less-than':
      return `all numbers less than ${a}`
    case 'less-equal':
      return `all numbers less than or equal to ${a}`
    case 'between-open':
      return `all numbers between ${a} and ${b}`
    case 'between-closed':
      return `all numbers between ${a} and ${b}, including ${a} and ${b}`
    case 'between-left-closed':
      return `all numbers between ${a} and ${b}, including ${a} but not ${b}`
    case 'between-right-closed':
      return `all numbers between ${a} and ${b}, including ${b} but not ${a}`
    default:
      return 'all real numbers'
  }
}

function generateSetBuilderNotation(
  statementType: string,
  a: number,
  b: number,
  variable: 'x' | 'y' = 'x',
): string {
  switch (statementType) {
    case 'all-real':
      return `{${variable} | ${variable} ∈ ℝ}`
    case 'greater-than':
      return `{${variable} | ${variable} > ${a}}`
    case 'greater-equal':
      return `{${variable} | ${variable} ≥ ${a}}`
    case 'less-than':
      return `{${variable} | ${variable} < ${a}}`
    case 'less-equal':
      return `{${variable} | ${variable} ≤ ${a}}`
    case 'between-open':
      return `{${variable} | ${a} < ${variable} < ${b}}`
    case 'between-closed':
      return `{${variable} | ${a} ≤ ${variable} ≤ ${b}}`
    case 'between-left-closed':
      return `{${variable} | ${a} ≤ ${variable} < ${b}}`
    case 'between-right-closed':
      return `{${variable} | ${a} < ${variable} ≤ ${b}}`
    default:
      return `{${variable} | ${variable} ∈ ℝ}`
  }
}

function generateIntervalNotation(statementType: string, a: number, b: number): string {
  switch (statementType) {
    case 'all-real':
      return '(-∞, ∞)'
    case 'greater-than':
      return `(${a}, ∞)`
    case 'greater-equal':
      return `[${a}, ∞)`
    case 'less-than':
      return `(-∞, ${a})`
    case 'less-equal':
      return `(-∞, ${a}]`
    case 'between-open':
      return `(${a}, ${b})`
    case 'between-closed':
      return `[${a}, ${b}]`
    case 'between-left-closed':
      return `[${a}, ${b})`
    case 'between-right-closed':
      return `(${a}, ${b}]`
    default:
      return '(-∞, ∞)'
  }
}

/**
 * Generate display-only domain/range problems for teacher assessment
 */
export function generateDisplayDomainRangeProblem(): DisplayDomainRangeProblem {
  const id = `display-domain-range-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

  const functionTypes: FunctionType[] = ['linear', 'quadratic', 'cubic', 'absolute', 'square-root']
  const functionType = functionTypes[randomInt(0, functionTypes.length - 1)]!

  const questionTypes: Array<'domain' | 'range'> = ['domain', 'range']
  const questionType = questionTypes[randomInt(0, questionTypes.length - 1)]!

  // Generate function equation and calculate domain/range
  const functionData = generateFunctionWithDomainRange(functionType)

  return {
    id,
    functionType,
    equation: functionData.equation,
    questionType,
    description: `Find the ${questionType.charAt(0).toUpperCase() + questionType.slice(1)}`,
    answer: {
      domain: functionData.domain,
      range: functionData.range,
      domainSetBuilder: intervalToSetBuilder(functionData.domain, 'x'),
      rangeSetBuilder: intervalToSetBuilder(functionData.range, 'y'),
    },
  }
}

/**
 * Convert interval notation to set-builder notation
 * @param interval - The interval notation string
 * @param variable - The variable to use in set-builder notation ('x' for domain, 'y' for range)
 */
function intervalToSetBuilder(interval: string, variable: 'x' | 'y' = 'x'): string {
  // Handle special cases
  if (interval === '(-∞, ∞)') {
    return `{${variable} | ${variable} ∈ ℝ}`
  }

  // Parse interval notation patterns
  const patterns = [
    {
      regex: /^\[(-?\d+(?:\/\d+)?), ∞\)$/,
      template: (match: RegExpMatchArray) => `{${variable} | ${variable} ≥ ${match[1]}}`,
    },
    {
      regex: /^\((-?\d+(?:\/\d+)?), ∞\)$/,
      template: (match: RegExpMatchArray) => `{${variable} | ${variable} > ${match[1]}}`,
    },
    {
      regex: /^\(-∞, (-?\d+(?:\/\d+)?)\]$/,
      template: (match: RegExpMatchArray) => `{${variable} | ${variable} ≤ ${match[1]}}`,
    },
    {
      regex: /^\(-∞, (-?\d+(?:\/\d+)?)\)$/,
      template: (match: RegExpMatchArray) => `{${variable} | ${variable} < ${match[1]}}`,
    },
    {
      regex: /^\[(-?\d+(?:\/\d+)?), (-?\d+(?:\/\d+)?)\]$/,
      template: (match: RegExpMatchArray) =>
        `{${variable} | ${match[1]} ≤ ${variable} ≤ ${match[2]}}`,
    },
    {
      regex: /^\((-?\d+(?:\/\d+)?), (-?\d+(?:\/\d+)?)\)$/,
      template: (match: RegExpMatchArray) =>
        `{${variable} | ${match[1]} < ${variable} < ${match[2]}}`,
    },
    {
      regex: /^\[(-?\d+(?:\/\d+)?), (-?\d+(?:\/\d+)?)\)$/,
      template: (match: RegExpMatchArray) =>
        `{${variable} | ${match[1]} ≤ ${variable} < ${match[2]}}`,
    },
    {
      regex: /^\((-?\d+(?:\/\d+)?), (-?\d+(?:\/\d+)?)\]$/,
      template: (match: RegExpMatchArray) =>
        `{${variable} | ${match[1]} < ${variable} ≤ ${match[2]}}`,
    },
  ]

  for (const pattern of patterns) {
    const match = interval.match(pattern.regex)
    if (match) {
      return pattern.template(match)
    }
  }

  // Fallback for unrecognized patterns
  return `{${variable} | ${variable} ∈ ℝ}`
}

/**
 * Generate function with domain and range calculations
 */
function generateFunctionWithDomainRange(type: FunctionType): {
  equation: string
  domain: string
  range: string
} {
  const a = randomCoeff()
  const b = randomInt(-5, 5)
  const c = randomInt(-5, 5)
  const h = randomInt(-3, 3)
  const k = randomInt(-3, 3)

  switch (type) {
    case 'linear':
      // f(x) = ax + b
      return {
        equation: `f(x) = ${formatCoeff(a)}x${formatConstant(b)}`,
        domain: '(-∞, ∞)',
        range: '(-∞, ∞)',
      }

    case 'quadratic': {
      // f(x) = ax² + bx + c
      // Calculate vertex using exact arithmetic to avoid floating point errors
      // vertex_x = -b/(2a), vertex_y = a*(-b/(2a))² + b*(-b/(2a)) + c
      // Simplifying: vertex_y = a*b²/(4a²) - b²/(2a) + c = b²/(4a) - b²/(2a) + c = -b²/(4a) + c
      const vertex_y_numerator = -b * b + 4 * a * c
      const vertex_y_denominator = 4 * a
      const vertex_y_fraction = formatFraction(vertex_y_numerator, vertex_y_denominator)

      return {
        equation: `f(x) = ${formatCoeff(a)}x²${formatTerm(b, 'x')}${formatConstant(c)}`,
        domain: '(-∞, ∞)',
        range: a > 0 ? `[${vertex_y_fraction}, ∞)` : `(-∞, ${vertex_y_fraction}]`,
      }
    }

    case 'cubic': {
      // f(x) = ax³ + bx² + cx + d
      const d = randomInt(-5, 5)
      return {
        equation: `f(x) = ${formatCoeff(a)}x³${formatTerm(b, 'x²')}${formatTerm(c, 'x')}${formatConstant(d)}`,
        domain: '(-∞, ∞)',
        range: '(-∞, ∞)',
      }
    }

    case 'absolute':
      // f(x) = a|x - h| + k
      return {
        equation: `f(x) = ${formatCoeff(a)}|x${formatConstant(-h, true)}|${formatConstant(k)}`,
        domain: '(-∞, ∞)',
        range: a > 0 ? `[${k}, ∞)` : `(-∞, ${k}]`,
      }

    case 'square-root':
      // f(x) = a√(x - h) + k
      return {
        equation: `f(x) = ${formatCoeff(a)}√(x${formatConstant(-h, true)})${formatConstant(k)}`,
        domain: `[${h}, ∞)`,
        range: a > 0 ? `[${k}, ∞)` : `(-∞, ${k}]`,
      }

    default:
      return {
        equation: 'f(x) = x',
        domain: '(-∞, ∞)',
        range: '(-∞, ∞)',
      }
  }
}

/**
 * Format coefficient for display
 */
function formatCoeff(coeff: number): string {
  if (coeff === 1) return ''
  if (coeff === -1) return '-'
  return coeff.toString()
}

/**
 * Format term with coefficient
 */
function formatTerm(coeff: number, variable: string): string {
  if (coeff === 0) return ''
  if (coeff > 0) return ` + ${coeff === 1 ? '' : coeff}${variable}`
  return ` - ${coeff === -1 ? '' : Math.abs(coeff)}${variable}`
}

/**
 * Format constant term
 */
function formatConstant(constant: number, forceSign: boolean = false): string {
  if (constant === 0 && !forceSign) return ''
  if (constant === 0 && forceSign) return ` + 0`
  if (constant > 0) return ` + ${constant}`
  return ` - ${Math.abs(constant)}`
}

/**
 * Format a fraction in simplest form
 */
function formatFraction(numerator: number, denominator: number): string {
  // Handle special cases
  if (denominator === 0) return 'undefined'
  if (numerator === 0) return '0'

  // Handle negative fractions
  const isNegative = numerator < 0 !== denominator < 0
  numerator = Math.abs(numerator)
  denominator = Math.abs(denominator)

  // Find GCD to simplify fraction
  const gcd = (a: number, b: number): number => (b === 0 ? a : gcd(b, a % b))
  const commonDivisor = gcd(numerator, denominator)

  numerator = numerator / commonDivisor
  denominator = denominator / commonDivisor

  // Format the result
  const sign = isNegative ? '-' : ''

  if (denominator === 1) {
    return `${sign}${numerator}`
  }

  return `${sign}${numerator}/${denominator}`
}

/**
 * Generate display-only piecewise function problems for teacher assessment
 */
export function generateDisplayPiecewiseProblem(): DisplayPiecewiseProblem {
  const id = `display-piecewise-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

  // Choose random function letter
  const functionLetters: FunctionLetter[] = ['f', 'g', 'h', 'p', 'q']
  const functionLetter = functionLetters[randomInt(0, functionLetters.length - 1)] as FunctionLetter

  // Generate 3-5 pieces
  const numPieces = randomInt(3, 5)
  const pieces = generatePiecewisePieces(numPieces)

  // Choose evaluation point that falls within one of the intervals
  const evaluationPoint = chooseEvaluationPoint(pieces)

  // Find the applicable piece and calculate the answer
  const answer = calculatePiecewiseAnswer(pieces, evaluationPoint, functionLetter)

  return {
    id,
    functionLetter,
    pieces,
    evaluationPoint,
    description: `Find ${functionLetter}(${evaluationPoint})`,
    answer,
  }
}

/**
 * Generate non-overlapping piecewise function pieces
 */
function generatePiecewisePieces(numPieces: number): PiecewisePiece[] {
  const pieces: PiecewisePiece[] = []

  // Decide whether to use single intervals (like x > 5) or double intervals (like -2 ≤ x < 1)
  const useSingleIntervals = randomInt(0, 1) === 1 && numPieces <= 3

  if (useSingleIntervals) {
    return generateSingleIntervalPieces(numPieces)
  }

  // Create a reasonable domain range (e.g., -6 to 6)
  const domainStart = -6
  const domainEnd = 6
  const totalRange = domainEnd - domainStart

  // Divide the domain into non-overlapping intervals
  const intervalSize = totalRange / numPieces

  for (let i = 0; i < numPieces; i++) {
    const leftBound = Math.round(domainStart + i * intervalSize)
    const rightBound =
      i === numPieces - 1 ? domainEnd : Math.round(domainStart + (i + 1) * intervalSize)

    // Determine inclusivity - avoid overlaps and ensure boundary point clarity
    let leftInclusive = i === 0 || randomInt(0, 1) === 1
    let rightInclusive = i === numPieces - 1 ? false : randomInt(0, 1) === 1

    // Ensure no gaps or overlaps - fix boundary point clarity
    if (i > 0) {
      const prevPiece = pieces[i - 1]
      if (prevPiece) {
        // If previous piece includes right bound, current piece should not include left bound
        if (prevPiece.rightInclusive) {
          leftInclusive = false
        } else {
          // If previous piece doesn't include right bound, current piece must include left bound
          leftInclusive = true
        }
      }
    }

    const functionType = generateRandomFunctionType()
    const equation = generatePieceEquation(functionType, leftBound, rightBound)
    const condition = formatCondition(leftBound, rightBound, leftInclusive, rightInclusive)

    pieces.push({
      functionType,
      equation,
      condition,
      leftBound,
      rightBound,
      leftInclusive,
      rightInclusive,
    })
  }

  return pieces
}

/**
 * Generate piecewise pieces with single intervals (like x > 5, x ≤ 3)
 */
function generateSingleIntervalPieces(numPieces: number): PiecewisePiece[] {
  const pieces: PiecewisePiece[] = []

  // Generate boundary points
  const boundaries: number[] = []
  for (let i = 0; i < numPieces - 1; i++) {
    boundaries.push(randomInt(-5, 5))
  }
  boundaries.sort((a, b) => a - b)

  for (let i = 0; i < numPieces; i++) {
    let condition: string
    let leftBound: number
    let rightBound: number
    let leftInclusive: boolean
    let rightInclusive: boolean

    if (i === 0) {
      // First piece: x ≤ boundary or x < boundary
      rightBound = boundaries[0] as number
      leftBound = -1000 // Use large negative number instead of -Infinity
      leftInclusive = false
      rightInclusive = randomInt(0, 1) === 1
      condition = `x ${rightInclusive ? '≤' : '<'} ${rightBound}`
    } else if (i === numPieces - 1) {
      // Last piece: x > boundary or x ≥ boundary
      leftBound = boundaries[i - 1] as number
      rightBound = 1000 // Use large positive number instead of Infinity

      // Ensure boundary clarity: if previous piece includes the boundary, this piece should not
      const prevPiece = pieces[i - 1]
      if (prevPiece?.rightInclusive) {
        leftInclusive = false
      } else {
        leftInclusive = true
      }
      rightInclusive = false
      condition = `x ${leftInclusive ? '≥' : '>'} ${leftBound}`
    } else {
      // Middle piece: boundary1 < x ≤ boundary2 (or similar)
      leftBound = boundaries[i - 1] as number
      rightBound = boundaries[i] as number

      // Ensure boundary clarity for left bound
      const prevPiece = pieces[i - 1]
      if (prevPiece?.rightInclusive) {
        leftInclusive = false
      } else {
        leftInclusive = true
      }
      rightInclusive = randomInt(0, 1) === 1

      const leftSymbol = leftInclusive ? '≤' : '<'
      const rightSymbol = rightInclusive ? '≤' : '<'
      condition = `${leftBound} ${leftSymbol} x ${rightSymbol} ${rightBound}`
    }

    const functionType = generateRandomFunctionType()
    const equation = generatePieceEquation(functionType, leftBound, rightBound)

    pieces.push({
      functionType,
      equation,
      condition,
      leftBound,
      rightBound,
      leftInclusive,
      rightInclusive,
    })
  }

  return pieces
}

/**
 * Generate random function type for piecewise pieces
 */
function generateRandomFunctionType(): FunctionType {
  const types: FunctionType[] = ['linear', 'quadratic', 'constant', 'absolute', 'square-root']
  return types[randomInt(0, types.length - 1)] as FunctionType
}

/**
 * Generate equation for a piecewise function piece
 */
function generatePieceEquation(
  type: FunctionType,
  leftBound?: number,
  rightBound?: number,
): string {
  const a = randomCoeff()
  const b = randomInt(-5, 5)
  const c = randomInt(-5, 5)
  let h = randomInt(-3, 3)

  // For square root functions, ensure the radicand is non-negative within the interval
  if (type === 'square-root' && leftBound !== undefined && rightBound !== undefined) {
    // Calculate h such that x - h >= 0 for all x in [leftBound, rightBound]
    // This means h <= leftBound (so that leftBound - h >= 0)
    h = Math.min(h, leftBound - 1)
  }

  switch (type) {
    case 'constant':
      return `${c}`

    case 'linear':
      return `${formatCoeff(a)}x${formatConstant(b)}`

    case 'quadratic':
      return `${formatCoeff(a)}x²${formatTerm(b, 'x')}${formatConstant(c)}`

    case 'absolute':
      return `${formatCoeff(a)}|x${formatConstant(-h, true)}|${formatConstant(c)}`

    case 'square-root': {
      // Generate square root with proper mathematical notation and valid domain
      const radicand = h === 0 ? 'x' : `x${formatConstant(-h, true)}`
      const coefficient = formatCoeff(a)
      const constant = formatConstant(c)
      return `${coefficient}√(${radicand})${constant}`
    }

    default:
      return `x`
  }
}

/**
 * Format condition string for piecewise piece
 */
function formatCondition(
  leftBound: number,
  rightBound: number,
  leftInclusive: boolean,
  rightInclusive: boolean,
): string {
  const leftSymbol = leftInclusive ? '≤' : '<'
  const rightSymbol = rightInclusive ? '≤' : '<'

  return `${leftBound} ${leftSymbol} x ${rightSymbol} ${rightBound}`
}

/**
 * Choose evaluation point that falls within one of the intervals
 */
function chooseEvaluationPoint(pieces: PiecewisePiece[]): number {
  // Pick a random piece
  const piece = pieces[randomInt(0, pieces.length - 1)] as PiecewisePiece

  // Choose a point within that piece's interval
  let point: number
  if (piece.leftBound === piece.rightBound) {
    point = piece.leftBound
  } else {
    // Handle large bounds (single intervals)
    const effectiveLeftBound = Math.max(piece.leftBound, -10)
    const effectiveRightBound = Math.min(piece.rightBound, 10)

    // Choose a point that satisfies the interval conditions
    const candidates: number[] = []

    for (let x = effectiveLeftBound; x <= effectiveRightBound; x++) {
      const satisfiesLeft = piece.leftInclusive ? x >= piece.leftBound : x > piece.leftBound
      const satisfiesRight = piece.rightInclusive ? x <= piece.rightBound : x < piece.rightBound

      // For square root functions, ensure the radicand is non-negative
      let validForSquareRoot = true
      if (piece.functionType === 'square-root') {
        // Extract h from the equation to check if x - h >= 0
        const sqrtMatch = piece.equation.match(/√\(x([+-]\d+)\)/)
        if (sqrtMatch) {
          const h = sqrtMatch[1] ? -Number.parseInt(sqrtMatch[1], 10) : 0
          validForSquareRoot = x - h >= 0
        }
      }

      if (satisfiesLeft && satisfiesRight && validForSquareRoot) {
        candidates.push(x)
      }
    }

    point =
      candidates.length > 0
        ? (candidates[randomInt(0, candidates.length - 1)] as number)
        : effectiveLeftBound
  }

  return point
}

/**
 * Calculate the answer for a piecewise function evaluation
 */
function calculatePiecewiseAnswer(
  pieces: PiecewisePiece[],
  evaluationPoint: number,
  functionLetter: FunctionLetter,
): PiecewiseAnswer {
  // Find the applicable piece
  const applicablePiece = pieces.find((piece) => {
    const satisfiesLeft = piece.leftInclusive
      ? evaluationPoint >= piece.leftBound
      : evaluationPoint > piece.leftBound
    const satisfiesRight = piece.rightInclusive
      ? evaluationPoint <= piece.rightBound
      : evaluationPoint < piece.rightBound

    return satisfiesLeft && satisfiesRight
  })

  if (!applicablePiece) {
    throw new Error(`No applicable piece found for x = ${evaluationPoint}`)
  }

  // Calculate the result using exact arithmetic
  const result = evaluatePieceAtPoint(applicablePiece, evaluationPoint)

  // Format substitution step with proper mathematical notation
  const substitutedEquation = formatSubstitutedEquation(applicablePiece.equation, evaluationPoint)
  const substitutionStep = `${functionLetter}(${evaluationPoint}) = ${substitutedEquation}`

  // Create explanation with clear boundary point reasoning
  const explanation = `Since ${evaluationPoint} satisfies the condition "${applicablePiece.condition}", we use the piece ${functionLetter}(x) = ${applicablePiece.equation}`

  return {
    applicablePiece,
    substitutionStep,
    finalResult: result.toString(),
    explanation,
  }
}

/**
 * Evaluate a piecewise function piece at a specific point using exact arithmetic
 */
function evaluatePieceAtPoint(piece: PiecewisePiece, x: number): number {
  const { functionType, equation } = piece

  // Use a more direct approach based on function type and known generation patterns
  switch (functionType) {
    case 'constant': {
      // For constants, just parse the number directly
      const num = Number.parseFloat(equation.trim())
      return Number.isNaN(num) ? 0 : num
    }

    case 'linear': {
      // Use direct evaluation by substituting x
      try {
        // Replace x with the actual value and evaluate
        let expression = equation.replace(/x/g, `(${x})`)
        // Handle coefficient formatting (e.g., "2x" becomes "2*(x)")
        expression = expression.replace(/(\d+)\(/g, '$1*(')
        // Handle negative signs properly
        expression = expression.replace(/\s*\+\s*/g, '+').replace(/\s*-\s*/g, '-')
        // Use Function constructor for safe evaluation of mathematical expressions
        const result = new Function(`return ${expression}`)()
        return Number.isNaN(result) ? x : result
      } catch (error) {
        console.warn('Linear evaluation error:', error, 'for equation:', equation, 'at x =', x)
        // Fallback: assume it's just x
        return x
      }
    }

    case 'quadratic': {
      try {
        // Replace x with the actual value and evaluate
        // Handle x² by converting to x*x for evaluation
        let expression = equation.replace(/x²/g, `(${x})*(${x})`).replace(/x/g, `(${x})`)
        // Handle coefficient formatting (e.g., "2x" becomes "2*(x)")
        expression = expression.replace(/(\d+)\(/g, '$1*(')
        // Handle negative signs properly
        expression = expression.replace(/\s*\+\s*/g, '+').replace(/\s*-\s*/g, '-')
        const result = new Function(`return ${expression}`)()
        return Number.isNaN(result) ? x * x : result
      } catch (error) {
        console.warn('Quadratic evaluation error:', error, 'for equation:', equation, 'at x =', x)
        // Fallback: assume it's just x²
        return x * x
      }
    }

    case 'absolute': {
      try {
        // Replace x with the actual value and evaluate
        // Handle absolute value by using Math.abs
        let expression = equation.replace(/x/g, `(${x})`)
        // Convert |expression| to Math.abs(expression)
        expression = expression.replace(/\|([^|]+)\|/g, 'Math.abs($1)')
        // Handle coefficient formatting BEFORE Math.abs (e.g., "2Math.abs" becomes "2*Math.abs")
        expression = expression.replace(/(\d+)Math\.abs/g, '$1*Math.abs')
        // Handle coefficient formatting for other cases (e.g., "2x" becomes "2*(x)")
        expression = expression.replace(/(\d+)\(/g, '$1*(')
        // Handle negative signs properly
        expression = expression.replace(/\s*\+\s*/g, '+').replace(/\s*-\s*/g, '-')
        const result = new Function(`return ${expression}`)()
        return Number.isNaN(result) ? Math.abs(x) : result
      } catch (error) {
        console.warn(
          'Absolute value evaluation error:',
          error,
          'for equation:',
          equation,
          'at x =',
          x,
        )
        // Fallback: assume it's just |x|
        return Math.abs(x)
      }
    }

    case 'square-root': {
      try {
        // Replace x with the actual value and evaluate
        // Handle square root by using Math.sqrt
        let expression = equation.replace(/x/g, `(${x})`)
        // Convert √(expression) to Math.sqrt(expression)
        expression = expression.replace(/√\(([^)]+)\)/g, 'Math.sqrt($1)')
        // Handle coefficient formatting BEFORE Math.sqrt (e.g., "2Math.sqrt" becomes "2*Math.sqrt")
        expression = expression.replace(/(\d+)Math\.sqrt/g, '$1*Math.sqrt')
        // Handle coefficient formatting for other cases (e.g., "2x" becomes "2*(x)")
        expression = expression.replace(/(\d+)\(/g, '$1*(')
        // Handle negative signs properly
        expression = expression.replace(/\s*\+\s*/g, '+').replace(/\s*-\s*/g, '-')

        // Check if the radicand would be negative before evaluation
        const radicandMatch = expression.match(/Math\.sqrt\(([^)]+)\)/)
        if (radicandMatch) {
          const radicandValue = new Function(`return ${radicandMatch[1]}`)()
          if (radicandValue < 0) {
            return Number.NaN
          }
        }

        const result = new Function(`return ${expression}`)()
        return Number.isNaN(result) ? Math.sqrt(x >= 0 ? x : Number.NaN) : result
      } catch (error) {
        console.warn('Square root evaluation error:', error, 'for equation:', equation, 'at x =', x)
        // Fallback: assume it's just √x
        return Math.sqrt(x >= 0 ? x : Number.NaN)
      }
    }

    default:
      return x
  }
}

/**
 * Format substituted equation with proper mathematical notation
 */
function formatSubstitutedEquation(equation: string, value: number): string {
  // Handle different function types with proper substitution
  return (
    equation
      // Replace x with the value in parentheses for clarity
      .replace(/x/g, `(${value})`)
      // Handle square root expressions specially
      .replace(/√\(([^)]+)\)/g, (_, radicand) => {
        // Replace any remaining x in the radicand
        const substitutedRadicand = radicand.replace(/\((\d+)\)/g, '$1')
        return `√(${substitutedRadicand})`
      })
      // Clean up double parentheses that might occur
      .replace(/\(\((\d+)\)\)/g, '($1)')
      // Handle absolute value expressions
      .replace(/\|([^|]+)\|/g, (_, content) => {
        const substitutedContent = content.replace(/\((\d+)\)/g, '$1')
        return `|${substitutedContent}|`
      })
  )
}
